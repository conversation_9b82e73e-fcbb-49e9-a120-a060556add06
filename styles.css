/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    height: 100vh;
    overflow: hidden;
    background: transparent;
}

/* 背景视频 */
#background-video {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

/* 主容器 */
.container {
    height: 100vh;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.1);
}

/* 对话容器 */
.chat-container {
    width: 400px;
    height: 80vh;
    background: transparent;
    backdrop-filter: blur(5px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 对话标题 */
.chat-header {
    padding: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 对话消息区域 */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* 消息样式 */
.message {
    display: flex;
    gap: 12px;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    flex-shrink: 0;
}

.ai-avatar, .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.ai-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-avatar {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.message-content {
    flex: 1;
    max-width: 280px;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.ai-message .message-bubble {
    background: rgba(102, 126, 234, 0.5);
    color: rgba(255, 255, 255, 0.95);
    border-bottom-left-radius: 6px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-message .message-bubble {
    background: rgba(102, 126, 234, 0.5);
    color: rgba(255, 255, 255, 0.95);
    border-bottom-right-radius: 6px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-time {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 4px;
    padding: 0 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-message .message-time {
    text-align: right;
}

/* 输入区域 */
.chat-input-container {
    padding: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 24px;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.4;
    padding: 8px 12px;
    background: transparent;
    max-height: 120px;
    min-height: 20px;
}

#messageInput::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.send-button {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-button:active {
    transform: scale(0.95);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
}

.char-count {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
        justify-content: center;
    }

    .chat-container {
        width: 100%;
        height: 90vh;
        max-width: 400px;
    }
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}
