/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    height: 100vh;
    overflow: hidden;
    background: transparent;
}

/* 背景视频 */
#background-video {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

/* 主容器 */
.container {
    height: 100vh;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.1);
}

/* 对话容器 */
.chat-container {
    width: 450px;
    height: 85vh;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: none;
    position: relative;
    transition: all 0.3s ease;
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 关闭按钮 */
.close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    color: rgba(0, 0, 0, 0.7);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
    backdrop-filter: blur(5px);
}

.close-button:hover {
    background: rgba(255, 255, 255, 0.95);
    color: rgba(0, 0, 0, 0.9);
    transform: scale(1.05);
}

/* 对话标题 */
.chat-header {
    padding: 15px 20px;
    background: rgba(0, 150, 255, 0.2);
    border-bottom: 1px solid rgba(0, 150, 255, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.chat-header h2 {
    color: rgba(255, 255, 255, 0.95);
    font-size: 16px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 对话消息区域 */
.chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: rgba(0, 0, 0, 0.1);
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* 消息样式 */
.message {
    display: flex;
    gap: 12px;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ai-message {
    margin-top: 15px;
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    flex-shrink: 0;
}

.ai-avatar, .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 500;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.ai-avatar {
    background: rgba(0, 150, 255, 0.8);
    border: 1px solid rgba(0, 150, 255, 0.3);
}

.user-avatar {
    background: rgba(100, 200, 100, 0.8);
    border: 1px solid rgba(100, 200, 100, 0.3);
}

.message-content {
    flex: 1;
    max-width: 280px;
}

.message-bubble {
    padding: 10px 14px;
    border-radius: 12px;
    font-size: 13px;
    line-height: 1.4;
    word-wrap: break-word;
    position: relative;
}

.ai-message .message-bubble {
    background: linear-gradient(135deg, rgba(200, 230, 255, 0.95) 0%, rgba(180, 220, 255, 0.9) 100%);
    color: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(100, 180, 255, 0.4);
    position: relative;
    margin-top: 25px;
    box-shadow: 0 4px 12px rgba(0, 100, 200, 0.15);
}

.ai-message .message-bubble::before {
    content: '讲师任运';
    position: absolute;
    top: -22px;
    left: 0;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: white;
    padding: 5px 14px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.user-message .message-bubble {
    background: rgba(100, 200, 100, 0.5);
    color: rgba(0, 0, 0, 0.85);
    border-bottom-right-radius: 4px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(100, 200, 100, 0.3);
}

.message-time {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 3px;
    padding: 0 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.user-message .message-time {
    text-align: right;
}

/* 输入区域 */
.chat-input-container {
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(0, 150, 255, 0.3);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 150, 255, 0.3);
    backdrop-filter: blur(5px);
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-size: 13px;
    line-height: 1.4;
    padding: 6px 8px;
    background: transparent;
    max-height: 100px;
    min-height: 18px;
    color: rgba(0, 0, 0, 0.8);
}

#messageInput::placeholder {
    color: rgba(0, 0, 0, 0.4);
}

.send-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: rgba(0, 150, 255, 0.8);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    border: 1px solid rgba(0, 150, 255, 0.3);
}

.send-button:hover {
    background: rgba(0, 150, 255, 1);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 150, 255, 0.4);
}

.send-button:active {
    transform: scale(0.95);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
}

.char-count {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
        justify-content: center;
    }

    .chat-container {
        width: 100%;
        height: 90vh;
        max-width: 400px;
    }
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 流式响应光标动画 */
.cursor {
    animation: blink 1s infinite;
    color: rgba(255, 255, 255, 0.8);
    font-weight: bold;
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}
