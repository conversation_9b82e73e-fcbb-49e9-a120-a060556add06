class ChatApp {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatMessages = document.getElementById('chatMessages');
        this.charCount = document.querySelector('.char-count');
        this.closeButton = document.getElementById('closeButton');
        this.chatContainer = document.querySelector('.chat-container');

        this.initializeEventListeners();
        this.adjustTextareaHeight();
    }

    initializeEventListeners() {
        // 发送按钮点击事件
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // 关闭按钮点击事件
        this.closeButton.addEventListener('click', () => this.closeChat());

        // 输入框回车事件
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 输入框内容变化事件
        this.messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.adjustTextareaHeight();
            this.updateSendButtonState();
        });

        // 输入框获得焦点时的处理
        this.messageInput.addEventListener('focus', () => {
            this.messageInput.parentElement.style.borderColor = 'rgba(0, 150, 255, 0.6)';
        });

        this.messageInput.addEventListener('blur', () => {
            this.messageInput.parentElement.style.borderColor = 'rgba(0, 150, 255, 0.3)';
        });
    }

    updateCharCount() {
        const length = this.messageInput.value.length;
        this.charCount.textContent = `${length}/1000`;

        if (length > 900) {
            this.charCount.style.color = '#f5576c';
        } else if (length > 800) {
            this.charCount.style.color = '#ff9800';
        } else {
            this.charCount.style.color = '#999';
        }
    }

    adjustTextareaHeight() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    updateSendButtonState() {
        const hasContent = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = !hasContent;
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;

        // 添加用户消息
        this.addMessage(message, 'user');

        // 清空输入框
        this.messageInput.value = '';
        this.updateCharCount();
        this.adjustTextareaHeight();
        this.updateSendButtonState();

        // 显示AI正在输入的指示器
        this.showTypingIndicator();

        // 模拟AI回复（这里可以替换为实际的API调用）
        setTimeout(() => {
            this.hideTypingIndicator();
            this.addAIResponse(message);
        }, 1000 + Math.random() * 2000);
    }

    addMessage(content, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const currentTime = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-bubble">${this.escapeHtml(content)}</div>
                <div class="message-time">${currentTime}</div>
            </div>
        `;

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-message';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="message-bubble">
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;

        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingMessage = this.chatMessages.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }

    addAIResponse(userMessage) {
        // 这里是模拟的AI回复逻辑，实际使用时应该调用真实的AI API
        const responses = [
            "观察图片，你发现了什么奇怪的地方了吗？",
            "我理解你的问题。让我来帮助你解决这个问题。",
            "这是一个很有趣的话题！我来为你详细解释一下。",
            "根据你的描述，我建议你可以尝试以下几种方法...",
            "感谢你的提问！这个问题涉及到几个方面...",
            "我明白了你的需求，让我为你提供一些建议。"
        ];

        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        this.addMessage(randomResponse, 'ai');
    }

    closeChat() {
        // 添加关闭动画
        this.chatContainer.style.transform = 'scale(0.8)';
        this.chatContainer.style.opacity = '0';

        setTimeout(() => {
            this.chatContainer.style.display = 'none';
        }, 300);
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});

// 处理视频加载错误
document.getElementById('background-video').addEventListener('error', (e) => {
    console.warn('背景视频加载失败:', e);
    // 可以在这里设置备用背景
    document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
});
