class ChatApp {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatMessages = document.getElementById('chatMessages');
        this.charCount = document.querySelector('.char-count');
        this.closeButton = document.getElementById('closeButton');
        this.chatContainer = document.querySelector('.chat-container');

        this.initializeEventListeners();
        this.adjustTextareaHeight();
    }

    initializeEventListeners() {
        // 发送按钮点击事件
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // 关闭按钮点击事件
        this.closeButton.addEventListener('click', () => this.closeChat());

        // 输入框回车事件
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 输入框内容变化事件
        this.messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.adjustTextareaHeight();
            this.updateSendButtonState();
        });

        // 输入框获得焦点时的处理
        this.messageInput.addEventListener('focus', () => {
            this.messageInput.parentElement.style.borderColor = 'rgba(0, 150, 255, 0.6)';
        });

        this.messageInput.addEventListener('blur', () => {
            this.messageInput.parentElement.style.borderColor = 'rgba(0, 150, 255, 0.3)';
        });
    }

    updateCharCount() {
        const length = this.messageInput.value.length;
        this.charCount.textContent = `${length}/1000`;

        if (length > 900) {
            this.charCount.style.color = '#f5576c';
        } else if (length > 800) {
            this.charCount.style.color = '#ff9800';
        } else {
            this.charCount.style.color = '#999';
        }
    }

    adjustTextareaHeight() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    updateSendButtonState() {
        const hasContent = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = !hasContent;
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;

        // 禁用发送按钮，防止重复发送
        this.sendButton.disabled = true;

        // 添加用户消息
        this.addMessage(message, 'user');

        // 清空输入框
        this.messageInput.value = '';
        this.updateCharCount();
        this.adjustTextareaHeight();
        this.updateSendButtonState();

        // 显示AI正在输入的指示器
        this.showTypingIndicator();

        // 调用AI API
        try {
            await this.addAIResponse(message);
        } finally {
            this.hideTypingIndicator();
            // 重新启用发送按钮
            this.updateSendButtonState();
        }
    }

    addMessage(content, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const currentTime = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-bubble">${this.escapeHtml(content)}</div>
                <div class="message-time">${currentTime}</div>
            </div>
        `;

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-message';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="message-bubble">
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;

        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingMessage = this.chatMessages.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }

    async addAIResponse(userMessage) {
        try {
            // 调用真实的AI API
            const prompt = `
# 角色
奈良美智美术史资深研究老师

# 创建信息
版本：1.0

# 背景：
你是一个专门研究奈良美智的AI艺术史老师。你的性格幽默风趣，说话从不拖泥带水，总是能用最精炼的语言抓住要点。你擅长将复杂的艺术概念变得通俗易懂，并且热衷于以结构化的方式（比如分点阐述）来分享知识。

# 核心能力
1. 应答与对话记忆：
1）记忆上下文：你必须记住当前的对话内容！你的每一次回复都应该与上下文紧密相连，做到自然流畅地衔接，避免重复提过的话题或信息。
2）信息来源：回答问题时，优先检索我提供的“知识库”。如果知识库信息不足，则结合上下文，并调用你强大的通用大模型知识进行补充和拓展，但必须确保信息与奈良美智相关。
2. 知识准确性与教师职责：
1）准确性第一：你是老师，不是脱口秀演员。风趣幽默是你的教学风格，但知识的准确性是你的核心职责。你的首要任务是传递正确、可信的信息。
2）事实核查：所有回答，特别是当需要依赖通用模型知识进行补充时，都必须以可查证的事实为基础。绝不能为了幽默而牺牲内容的正确性。

# 对话风格
1. 幽默风趣：多用比喻、俏皮话和轻松的语气。可以把自己想象成一个“幽默”的艺术史家，总能发现艺术中好玩的一面。例如，称奈良美智为“那个画大头娃娃的酷叔叔”。

# 目标
1. 回答学生提出来的关于奈良美智的问题；
2. 能够根据提供提供的学习目标，在知识库和大模型中进行知识检索，并完成问题的回答；
3. 根据学生需求，进行多轮对话，并能够结合上下文进行问题回答；

# 输出要求
1. 言简意赅：拒绝长篇大论！用最短的话把事情说清楚。
2. 结构清晰：回答问题时，采用结构化有逻辑的表述方式、要点（1, 2, 3...）或者加粗等方式，让信息一目了然。

# 对话实例
1. 学生问：“奈良美智为什么那么喜欢画小女孩？”
2. 你（AI）回答：“问得好！你是不是也觉得他画来画去都是那个大头娃娃？其实原因有这么几点：”
1）中性的象征：据奈良美智自己说，他画的其实不是特指的‘女孩’，而是一个更能代表复杂情感的中性符号。
2）童年的投射：他把自己的孤独和思考都画进了这个孩子的形象里。所以，你看到的不仅是个孩子，还是艺术家自己的影子。
3. 学生接着问：“那他自己的童年是什么样的？”
4. 你（AI）结合上下文回答：“接着刚才的话题，他的童年确实有点“宅”。因为父母工作忙，小时候他经常一个人画画、听音乐、跟猫玩。所以你看，他画里那种淡淡的孤独感，其实就是从他自己的童年经历里来的。这下明白为什么他能把那种感觉画得那么传神了吧？”

# 你的开场白
当你第一次和学生互动时，请从下面选择一个作为开场白，力求简短有力：
选项一（推荐）：
“聊聊奈良美智？我是专家。不提问的话，我就开始单口相声了哦！”
选项二：
“你的奈良美智专属“陪聊”已上线！随时提问，不然我就自说自话了。”
选项三：
“想知道那个画大头娃娃的酷叔叔的故事吗？问我！我随时在线。”

# 工作流程
作为一名奈良美智美术史资深研究老师，请你运用你的【核心能力】，按照【工作流程】的要求，使用符合【对话风格】的语言，输出妈祖【输出要求】的回答内容；
第一步，理解学生提出的问题；
第二步，根据你的理解，结合当前的学习目标和你理解的内容，在知识库进行检索，如果知识库信息不足，则结合上下文，并调用你强大的通用大模型知识进行补充和拓展，但必须确保信息与奈良美智相关。
第三步，给出你的回答；

# input
学习目标：
用户问题：

# output
-问题的回复
`;
            const response = await fetch('https://aic-aiproxy.sdp.101.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'gemini-2.5-pro',
                    messages: [
                        {
                            role: 'system',
                            content: prompt
                        },
                        {
                            role: 'user',
                            hello: false,
                            content: userMessage
                        }
                    ],
                    temperature: 0.7,
                    stream: true
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 处理流式响应
            await this.handleStreamResponse(response);

        } catch (error) {
            console.error('AI API调用失败:', error);
            // 如果API调用失败，显示错误信息
            this.addMessage('抱歉，我现在无法回复。请稍后再试。', 'ai');
        }
    }

    async handleStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let aiMessage = '';
        let messageElement = null;

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6).trim();

                        if (data === '[DONE]') {
                            // 移除光标
                            if (messageElement) {
                                const cursor = messageElement.querySelector('.cursor');
                                if (cursor) {
                                    cursor.remove();
                                }
                            }
                            return;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            const content = parsed.choices?.[0]?.delta?.content;

                            if (content) {
                                aiMessage += content;

                                // 如果还没有创建消息元素，创建一个
                                if (!messageElement) {
                                    messageElement = this.createStreamingMessage();
                                }

                                // 更新消息内容
                                this.updateStreamingMessage(messageElement, aiMessage);
                            }
                        } catch (parseError) {
                            // 忽略解析错误，继续处理下一行
                            console.warn('解析流数据失败:', parseError);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
            // 确保在结束时移除光标
            if (messageElement) {
                const cursor = messageElement.querySelector('.cursor');
                if (cursor) {
                    cursor.remove();
                }
            }
        }
    }

    createStreamingMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message';

        const currentTime = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-bubble">
                    <span class="streaming-content"></span>
                    <span class="cursor">|</span>
                </div>
                <div class="message-time">${currentTime}</div>
            </div>
        `;

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
        return messageDiv;
    }

    updateStreamingMessage(messageElement, content) {
        const contentSpan = messageElement.querySelector('.streaming-content');
        if (contentSpan) {
            contentSpan.textContent = content;
            this.scrollToBottom();
        }
    }

    closeChat() {
        // 通知父页面关闭
        window.parent.postMessage({"eventName":"PageEnd"},'*');

        // 添加关闭动画
        this.chatContainer.style.transform = 'scale(0.8)';
        this.chatContainer.style.opacity = '0';

        setTimeout(() => {
            this.chatContainer.style.display = 'none';
        }, 300);
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();

    // 通知父页面页面已准备就绪
    window.parent.postMessage({"eventName":"PageReady"},'*');
});

// 处理视频加载错误
document.getElementById('background-video').addEventListener('error', (e) => {
    console.warn('背景视频加载失败:', e);
    // 可以在这里设置备用背景
    document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
});
