<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 对话助手</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 背景视频 -->
    <video id="background-video" autoplay muted loop>
        <source src="idle.mov" type="video/mp4">
        您的浏览器不支持视频播放。
    </video>

            <!-- 关闭按钮 -->
            <button class="close-button" id="closeButton">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>


    <!-- 主容器 -->
    <div class="container">
        <!-- 对话区域 -->
        <div class="chat-container">


            <!-- 对话内容区域 -->
            <div class="chat-messages" id="chatMessages">
                <!-- 欢迎消息 -->
                <div class="message ai-message">
                    <div class="message-content">
                        <div class="message-bubble">
                            你好，我的朋友！<br>
                            问我吧，我秒回~~~
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-container">
                <div class="input-wrapper">
                    <textarea
                        id="messageInput"
                        placeholder="输入你的消息..."
                        rows="1"
                        maxlength="1000"
                    ></textarea>
                    <button id="sendButton" class="send-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22,2 15,22 11,13 2,9"></polygon>
                        </svg>
                    </button>
                </div>
                <div class="input-footer">
                    <span class="char-count">0/1000</span>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
